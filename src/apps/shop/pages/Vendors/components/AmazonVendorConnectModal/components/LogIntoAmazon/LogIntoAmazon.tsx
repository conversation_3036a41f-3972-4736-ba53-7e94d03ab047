import { Button } from '@/libs/ui/Button/Button';
import { AMAZON_HIGHFIVE_APP_URL } from '../../constants';
import { t } from 'i18next';

interface LogIntoAmazonProps {
  onPrevious: () => void;
}

export const LogIntoAmazon = ({ onPrevious }: LogIntoAmazonProps) => {
  return (
    <>
      <Button
        variant="white"
        className="mb-4"
        href={AMAZON_HIGHFIVE_APP_URL}
        target="_blank"
      >
        {t('client.vendors.amazon.loginToAmazon')}
      </Button>
      <Button variant="unstyled" onClick={onPrevious}>
        <span className="underline">
          {t('client.vendors.amazon.previousStep')}
        </span>
      </Button>
    </>
  );
};
