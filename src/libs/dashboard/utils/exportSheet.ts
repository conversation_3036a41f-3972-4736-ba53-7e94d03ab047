import { fetchApi } from '@/libs/utils/api';
import dayjs from 'dayjs';

interface exportSheetProps {
  path: string;
  filename: string;
  filters?: Record<string, unknown>;
  format?: 'xlsx' | 'csv';
}
export const exportSheet = async ({
  path,
  filename,
  filters,
  format = 'xlsx',
}: exportSheetProps) => {
  try {
    const params = new URLSearchParams();

    Object.entries(filters || {}).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach((v) => params.append(`${key}[]`, String(v)));
      } else {
        params.set(key, String(value));
      }
    });

    const fullPath = `${path}?${params.toString()}`;

    console.log({ fullPath });

    const response = await fetchApi<Response>(fullPath, {
      method: 'GET',
      authStrategy: 'token',
      options: {
        headers: {
          Accept:
            format === 'csv'
              ? 'text/csv'
              : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',

          'Content-Type': '',
        },
      },
    });

    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = `${filename}-${dayjs().format('YYYY-MM-DD')}.${format}`;

    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Export failed:', error);
  }
};
